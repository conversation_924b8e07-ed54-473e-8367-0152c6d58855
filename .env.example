# Vibtrix Environment Variables Template
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# Database Configuration
POSTGRES_PRISMA_URL=postgresql://username:password@localhost:5432/database_name
POSTGRES_URL_NON_POOLING=postgresql://username:password@localhost:5432/database_name

# JWT Security
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long
JWT_ACCESS_TOKEN_EXPIRY=3600
JWT_REFRESH_TOKEN_EXPIRY=604800

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Application URLs
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000
PORT=3000

# Stream Chat Configuration
NEXT_PUBLIC_STREAM_KEY=your-stream-key
STREAM_SECRET=your-stream-secret

# UploadThing Configuration
NEXT_PUBLIC_UPLOADTHING_APP_ID=your-uploadthing-app-id
UPLOADTHING_SECRET=your-uploadthing-secret

# Security
CRON_SECRET=your-secure-cron-secret-key
ENCRYPTION_KEY=your-32-character-encryption-key
SESSION_SECRET=your-session-secret-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Firebase Configuration (Optional)
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your-vapid-key

# Firebase Admin SDK (Use environment variables instead of service account file)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"

# Payment Gateway Configuration
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# AI API Keys (Optional)
OPENROUTER_API_KEY=your-openrouter-api-key
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORG_ID=your-openai-org-id
GEMINI_API_KEY=your-gemini-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# Security Headers
CONTENT_SECURITY_POLICY_NONCE=your-csp-nonce
