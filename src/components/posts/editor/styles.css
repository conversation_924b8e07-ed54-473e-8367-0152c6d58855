.tiptap p.is-editor-empty:first-child::before {
  color: theme("colors.muted.foreground");
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.tiptap.ProseMirror {
  outline: none;
}

/* Remove focus styles from editor content */
.tiptap-editor-content {
  outline: none !important;
  box-shadow: none !important;
}

.tiptap-editor-content:focus-within {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}