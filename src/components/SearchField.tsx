"use client";

import { SearchIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { Input } from "./ui/input";

export default function SearchField() {
  const router = useRouter();

  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    const form = e.currentTarget;
    const q = (form.q as HTMLInputElement).value.trim();
    if (!q) return;
    router.push(`/search?q=${encodeURIComponent(q)}`);
  }

  return (
    <form onSubmit={handleSubmit} method="GET" action="/search" className="w-full">
      <div className="relative w-full">
        <Input
          name="q"
          placeholder="Search"
          className="pe-10 w-full h-9 text-sm md:text-base"
        />
        <SearchIcon className="absolute right-3 top-1/2 size-4 md:size-5 -translate-y-1/2 transform text-muted-foreground" />
      </div>
    </form>
  );
}
