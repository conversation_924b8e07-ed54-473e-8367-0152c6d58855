.page-content {
  max-width: 100%;
  overflow-x: auto;
}

.page-content img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: var(--radius);
}

.page-content h1 {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 1rem 0;
}

.page-content h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
}

.page-content h3 {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 1rem 0;
}

.page-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.page-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.page-content a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.page-content blockquote {
  border-left: 3px solid hsl(var(--primary));
  padding-left: 1rem;
  margin: 1rem 0;
  color: hsl(var(--muted-foreground));
}

.page-content p {
  margin: 0.5rem 0;
}

/* Code block styling */
.page-content pre {
  background-color: hsl(var(--muted));
  border-radius: var(--radius);
  padding: 1rem;
  margin: 1rem 0;
  font-family: monospace;
  white-space: pre-wrap;
  overflow-x: auto;
  position: relative;
}

.page-content pre code.language-html::before {
  content: 'HTML';
  position: absolute;
  top: 0.25rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  font-family: sans-serif;
}

/* Rendered HTML content */
.page-content .html-content {
  border: 1px dashed hsl(var(--border));
  padding: 1rem;
  margin: 1rem 0;
  border-radius: var(--radius);
  position: relative;
}

.page-content .html-content::before {
  content: 'Rendered HTML';
  position: absolute;
  top: -0.75rem;
  left: 0.5rem;
  font-size: 0.75rem;
  background-color: hsl(var(--background));
  padding: 0 0.5rem;
  color: hsl(var(--muted-foreground));
}
