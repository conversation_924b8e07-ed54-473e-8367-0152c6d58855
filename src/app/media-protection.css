/* Media Protection CSS */

/* Prevent context menu on media elements */
.screenshot-protected img,
.screenshot-protected video,
.media-container img,
.media-container video {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Prevent dragging of images */
.screenshot-protected img,
.media-container img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* Add overlay to prevent screenshots (not foolproof but adds a layer of protection) */
.screenshot-protected {
  position: relative;
}

.screenshot-protected::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

/* Style for watermark overlay */
.media-watermark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 5;
  opacity: 0.3;
  font-size: 2rem;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  transform: rotate(-45deg);
}
