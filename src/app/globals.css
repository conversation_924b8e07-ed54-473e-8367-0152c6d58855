@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    --background: 224, 5%, 95%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 30 100% 50%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 30 100% 50%;

    --radius: 1rem;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;

    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;

    --primary: 30 90% 45%;
    --primary-foreground: 30 80.4% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 85.7% 97.3%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 30 90% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {

  /* Mobile navigation styles */
  .mobile-nav {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 999999 !important; /* Even higher z-index */
    display: flex !important;
    justify-content: space-between !important; /* Ensure even spacing */
    align-items: center !important;
    height: 56px !important;
    background-color: hsl(var(--card)) !important;
    border-top: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15) !important; /* Stronger shadow */
    width: 100vw !important; /* Use viewport width */
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: translateZ(0) !important; /* Force hardware acceleration */
    will-change: transform !important; /* Optimize for animations */
    -webkit-backface-visibility: hidden !important; /* Fix for some mobile browsers */
    -webkit-transform: translate3d(0,0,0) !important; /* Additional fix for iOS */
  }

  /* Hide on desktop and tablet screens (anything above 640px) */
  @media (min-width: 640px) {
    .mobile-nav,
    .mobile-nav-container,
    .mobile-nav-wrapper,
    [class*="mobile-nav"] {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
      height: 0 !important;
      width: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      z-index: -9999 !important;
    }
  }

  .mobile-nav-backdrop {
    position: absolute !important;
    inset: 0 !important;
    z-index: -1 !important;
    backdrop-filter: blur(8px) !important;
  }

  /* Fix for iOS Safari to ensure the navigation stays at the bottom */
  @supports (-webkit-touch-callout: none) {
    .mobile-nav {
      padding-bottom: env(safe-area-inset-bottom, 0px) !important;
      height: calc(56px + env(safe-area-inset-bottom, 0px)) !important;
    }
  }

  /* Container for mobile navigation to ensure it's always visible */
  .mobile-nav-container {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 999999 !important;
    width: 100vw !important;
    height: 56px !important;
    pointer-events: none !important; /* Allow clicks to pass through the container */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* The nav itself will capture clicks */
  .mobile-nav {
    pointer-events: auto !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Additional fix for mobile devices */
  @media (max-width: 639px) {
    body {
      padding-bottom: 80px !important; /* Increased padding to prevent content from being hidden behind the nav */
      margin-bottom: 56px !important; /* Add margin to ensure space for the nav */
    }

    /* Ensure the main content doesn't overlap with the mobile nav */
    #__next, main, .min-h-screen {
      padding-bottom: 56px !important;
      margin-bottom: 56px !important;
    }

    /* Show mobile navigation only on mobile screens */
    .mobile-nav, .mobile-nav-container, .mobile-nav-wrapper {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  /* Explicitly hide on tablet and desktop */
  @media (min-width: 640px) {
    body {
      padding-bottom: 0 !important;
      margin-bottom: 0 !important;
    }
  }

  .animate-heart-beat {
    animation: heart-beat 1s ease-in-out forwards;
  }

  @keyframes heart-beat {
    0% {
      opacity: 0;
      transform: scale(0);
    }

    15% {
      opacity: 1;
      transform: scale(1.2);
    }

    30% {
      transform: scale(0.9);
    }

    45% {
      transform: scale(1.1);
    }

    60% {
      transform: scale(1);
    }

    100% {
      opacity: 0;
      transform: scale(1.5);
    }
  }

  /* Sticker-related CSS has been removed */

  /* Targeted fix for Safe section icons */
  .safe-icon-fix {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    color: white !important;
    fill: white !important;
    stroke: white !important;
    border-radius: 50% !important;
    padding: 12px !important;
    width: 48px !important;
    height: 48px !important;
    display: block !important;
    margin: 0 auto 16px auto !important;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Fix for progress bars or bottom lines */
  .progress-line,
  .bottom-line,
  .feature-progress,
  [class*="progress"]:not(.progress-bar) {
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%) !important;
    height: 4px !important;
    border-radius: 2px !important;
  }

  /* Specific fixes for common feature showcase patterns */
  .feature-card,
  .showcase-card,
  .info-card {
    position: relative;
  }

  .feature-card .icon-container,
  .showcase-card .icon-container {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    border-radius: 50% !important;
    padding: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 48px !important;
    height: 48px !important;
    margin: 0 auto 16px auto !important;
  }

  .feature-card .icon-container svg,
  .showcase-card .icon-container svg,
  .feature-card .icon-container i,
  .showcase-card .icon-container i {
    color: white !important;
    fill: white !important;
    stroke: white !important;
  }

  /* Fix for any SVG icons that might be invisible */
  svg[class*="shield"],
  svg[class*="safe"],
  .safe-icon svg,
  .security-icon svg {
    fill: #6366f1 !important;
    stroke: #6366f1 !important;
    color: #6366f1 !important;
  }

  /* Alternative fix for icons with white fill */
  svg[fill="white"],
  svg[fill="#ffffff"],
  svg[fill="#fff"] {
    fill: #6366f1 !important;
  }

  /* Fix for any iframe content (if the issue is in an iframe) */
  iframe .icon,
  iframe svg {
    filter: drop-shadow(0 0 0 #6366f1) !important;
  }

  /* Additional fixes for external content and third-party widgets */

  /* Fix for any div containing shield or safe icons */
  div[class*="safe"] svg,
  div[class*="shield"] svg,
  .safe-environment svg,
  .security-feature svg {
    fill: #6366f1 !important;
    stroke: #6366f1 !important;
    background: rgba(99, 102, 241, 0.1) !important;
    border-radius: 50% !important;
    padding: 4px !important;
  }

  /* Fix for progress indicators and bottom lines */
  .safe-section .progress,
  .feature-section .progress,
  div[class*="safe"] .progress,
  div[class*="progress-bar"] {
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%) !important;
    height: 4px !important;
    border-radius: 2px !important;
    width: 100% !important;
  }

  /* Force visibility for any hidden icons */
  .safe-section *[style*="color: white"],
  .safe-section *[style*="fill: white"],
  .feature-card *[style*="color: white"],
  .feature-card *[style*="fill: white"] {
    color: #6366f1 !important;
    fill: #6366f1 !important;
  }

  /* Ensure text is visible in safe sections */
  .safe-section,
  .feature-card,
  div[class*="safe"] {
    color: #374151 !important;
  }

  .safe-section h3,
  .safe-section h4,
  .feature-card h3,
  .feature-card h4 {
    color: #111827 !important;
    font-weight: 600 !important;
  }

  /* Fix for any external widget containers */
  [data-widget*="safe"],
  [data-feature*="safe"],
  .widget-container .safe,
  .onboarding-card .safe {
    background: rgba(99, 102, 241, 0.05) !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    border-radius: 8px !important;
  }
}