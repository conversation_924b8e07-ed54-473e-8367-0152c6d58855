<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vibtrix - Pitch Deck</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #ff6b00;
            --secondary-color: #333333;
            --accent-color: #444444;
            --text-color: #555555;
            --caption-color: #888888;
            --bg-color: #ffffff;
            --success-color: #10b981;
            --warning-color: #f59e0b;
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            background: white;
        }

        .reveal {
            font-family: "Inter", "Helvetica Neue", sans-serif;
            background: white;
            margin: 0 auto;
            max-width: 100vw;
            display: flex;
            justify-content: center;
        }

        /* Fallback in case Reveal.js doesn't load */
        .reveal .slides {
            display: block;
            width: 100%;
            margin: 0 auto;
            max-width: 100vw;
        }

        .reveal .slides section {
            display: block;
            width: 100%;
            min-height: 100vh;
            padding: 2rem;
            background: white;
        }

        .reveal .slides section {
            text-align: left;
            padding: 2rem;
            max-width: 1920px;
            margin: 0 auto;
            max-height: none;
            overflow: auto;
            box-sizing: border-box;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .reveal .slides section > * {
            width: 100%;
            max-width: 1920px;
        }



        .reveal h1, .reveal h2, .reveal h3 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 15px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        .reveal h1 {
            font-size: 2.7rem;
            font-weight: 700;
            color: var(--primary-color);
            text-align: center;
            line-height: 1.2;
            margin-bottom: 3rem;
        }

        .reveal h2 {
            font-size: 2.1rem;
            font-weight: 600;
            color: var(--secondary-color);
            line-height: 1.3;
            margin-bottom: 3rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }

        .reveal h3 {
            font-size: 1.5rem;
            font-weight: 500;
            color: var(--accent-color);
            line-height: 1.4;
            margin-bottom: 1.5rem;
        }

        .reveal p, .reveal li {
            font-size: 1.2rem;
            font-weight: 400;
            color: var(--text-color);
            line-height: 1.6;
            margin-bottom: 1.5rem;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .reveal small {
            font-size: 1.05rem;
            font-weight: 400;
            color: var(--caption-color);
            line-height: 1.4;
        }

        .title-slide {
            text-align: center !important;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white !important;
            padding: 2rem !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            min-height: 100vh !important;
            max-height: none !important;
            box-sizing: border-box !important;
            overflow: auto !important;
            max-width: 1920px;
            margin: 0 auto;
        }

        .title-slide h1 {
            color: white !important;
            font-size: 2.7rem !important;
            font-weight: 700 !important;
            margin-bottom: 2rem !important;
            line-height: 1.2 !important;
        }

        .tagline {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.4;
            color: white;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin: 3rem 0;
            width: 100%;
            max-width: 1920px;
            box-sizing: border-box;
        }

        .feature-card {
            background: var(--bg-color);
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-sizing: border-box;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.15);
            border-color: var(--primary-color);
        }

        .feature-card h4 {
            font-size: 1.5rem;
            font-weight: 500;
            margin: 0 0 1rem 0;
            color: var(--primary-color);
            line-height: 1.4;
        }

        .feature-card p {
            font-size: 1.2rem;
            font-weight: 400;
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
        }

        .feature-icon {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .metric-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin: 1rem;
            min-width: 200px;
            flex: 1;
            box-sizing: border-box;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-value {
            font-size: 2.7rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            line-height: 1.2;
            color: white;
        }

        .metric-label {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.9;
            line-height: 1.4;
            color: white;
        }

        .metrics-row {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin: 3rem 0;
            gap: 1rem;
            max-width: 1920px;
        }

        .highlight-box {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 3rem 0;
            text-align: center;
            word-wrap: break-word;
            overflow-wrap: break-word;
            box-sizing: border-box;
            max-width: 1920px;
        }

        .highlight-box h3 {
            color: white !important;
            font-size: 1.5rem !important;
            font-weight: 500 !important;
            margin-bottom: 1.5rem !important;
            line-height: 1.4 !important;
        }

        .highlight-box p {
            font-size: 1.2rem !important;
            font-weight: 400 !important;
            color: white !important;
            margin: 0;
            line-height: 1.6 !important;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 3rem 0;
            justify-content: center;
            max-width: 1920px;
        }

        .tech-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 1.05rem;
            font-weight: 500;
            font-family: "Inter", "Helvetica Neue", sans-serif;
            transition: transform 0.2s ease;
        }

        .tech-badge:hover {
            transform: scale(1.05);
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 12px;
            height: 24px;
            margin: 1.5rem 0;
            overflow: hidden;
            max-width: 1920px;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--success-color), var(--accent-color));
            height: 100%;
            border-radius: 12px;
            transition: width 0.3s ease;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: start;
            width: 100%;
            max-width: 1920px;
            margin: 3rem 0;
            box-sizing: border-box;
        }

        .bullet-point {
            display: flex;
            align-items: flex-start;
            margin: 1rem 0;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .bullet-point i {
            color: var(--primary-color);
            margin-right: 1rem;
            margin-top: 0.25rem;
            flex-shrink: 0;
            font-size: 1rem;
        }

        .bullet-point span {
            flex: 1;
            font-size: 1.2rem;
            font-weight: 400;
            color: var(--text-color);
            line-height: 1.6;
        }

        .cta-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
            margin: 1.5rem 1rem;
            font-family: "Inter", "Helvetica Neue", sans-serif;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.3);
        }

        .financial-table-container {
            width: 100%;
            max-width: 1920px;
            overflow-x: auto;
            margin: 3rem 0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background: var(--bg-color);
        }

        .financial-table {
            width: 100%;
            min-width: 600px;
            border-collapse: collapse;
            background: var(--bg-color);
            font-family: "Inter", "Helvetica Neue", sans-serif;
        }

        .financial-table th {
            background: var(--primary-color);
            color: white;
            padding: 1.5rem 2rem;
            text-align: left;
            font-size: 1.2rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .financial-table td {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            font-size: 1.2rem;
            font-weight: 400;
            color: var(--text-color);
            white-space: nowrap;
        }

        .financial-table tr:nth-child(even) {
            background: #f8fafc;
        }

        @media (max-width: 1024px) {
            .reveal .slides section {
                padding: 1.5rem;
                max-height: none;
                overflow: auto;
            }

            .feature-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .two-column {
                gap: 2rem;
            }

            .feature-card {
                min-height: 180px;
                padding: 1.5rem;
            }

            .reveal h1 {
                font-size: 2.4rem;
            }

            .reveal h2 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 768px) {
            .reveal .slides section {
                padding: 1rem;
                max-height: none;
                overflow: auto;
            }

            .two-column {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .metrics-row {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }

            .metric-card {
                margin: 0.5rem 0;
                min-width: 90%;
                max-width: 300px;
                padding: 1.5rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-card {
                min-height: 160px;
                padding: 1.5rem;
            }

            .title-slide {
                padding: 1rem !important;
            }

            .title-slide h1 {
                font-size: 2.1rem !important;
            }

            .tagline {
                font-size: 1.2rem;
            }

            .highlight-box {
                padding: 1.5rem;
                margin: 2rem 0;
            }

            .tech-stack {
                justify-content: center;
                gap: 0.75rem;
            }

            .tech-badge {
                font-size: 0.9rem;
                padding: 0.375rem 0.75rem;
            }

            .reveal h1 {
                font-size: 2.1rem;
            }

            .reveal h2 {
                font-size: 1.5rem;
            }

            .reveal h3 {
                font-size: 1.35rem;
            }
        }

        @media (max-width: 480px) {
            .reveal .slides section {
                padding: 0.75rem;
                max-height: none;
                overflow: auto;
            }

            .reveal h1 {
                font-size: 1.8rem;
            }

            .reveal h2 {
                font-size: 1.35rem;
                margin-bottom: 1rem;
            }

            .reveal h3 {
                font-size: 1.2rem;
                margin-bottom: 0.75rem;
            }

            .reveal p, .reveal li {
                font-size: 1.05rem;
            }

            .bullet-point {
                margin: 0.5rem 0;
            }

            .financial-table {
                min-width: 500px;
                font-size: 1.05rem;
            }

            .financial-table th,
            .financial-table td {
                padding: 1rem 1.5rem;
            }

            .feature-card {
                min-height: 140px;
                padding: 1rem;
            }

            .metric-card {
                padding: 1rem;
            }

            .highlight-box {
                padding: 1.5rem;
                margin: 2rem 0;
            }

            .title-slide {
                padding: 0.75rem !important;
            }

            .title-slide h1 {
                font-size: 1.8rem !important;
            }

            .tagline {
                font-size: 1.05rem;
            }

            .cta-button {
                margin: 0.5rem 0.25rem;
                padding: 0.75rem 1rem;
                font-size: 1.05rem;
            }
        }

        /* Enable scrolling within slides */
        .reveal .slides section {
            overflow-y: auto;
            max-height: 90vh;
            max-width: 1920px;
            margin: 0 auto;
        }

        /* Center content on very large screens */
        @media (min-width: 1921px) {
            .reveal {
                max-width: 1920px;
                margin: 0 auto;
            }

            .reveal .slides {
                margin: 0 auto;
                max-width: 1920px;
            }

            .reveal .slides section {
                margin: 0 auto;
                max-width: 1920px;
            }
        }

        /* Ensure all content fits within viewport */
        * {
            box-sizing: border-box;
        }

        /* Content wrapper with flexible constraints */
        .content-wrapper {
            max-width: 100%;
            max-height: none;
            overflow: auto;
        }

        /* Text overflow prevention */
        .reveal .slides section h2,
        .reveal .slides section h3,
        .reveal .slides section p,
        .reveal .slides section li {
            max-width: 100%;
            word-break: break-word;
            overflow-wrap: break-word;
            text-overflow: ellipsis;
        }

        /* Ultra-compact button styling */
        .cta-button {
            display: inline-block;
            margin: 3px 2px;
            white-space: nowrap;
            font-size: clamp(0.65em, 1.2vw, 0.8em);
            padding: clamp(6px, 1.2vw, 8px) clamp(8px, 1.5vw, 12px);
        }

        /* Constrained progress bars */
        .progress-bar {
            width: 100%;
            max-width: 100%;
            height: clamp(4px, 0.8vw, 8px);
        }

        /* Ultra-compact tech stack */
        .tech-stack {
            width: 100%;
            max-width: 100%;
        }

        .tech-badge {
            font-size: clamp(0.6em, 1.1vw, 0.7em);
            padding: clamp(3px, 0.6vw, 4px) clamp(6px, 1.2vw, 8px);
            margin: 1px;
        }

        /* ===== PDF CAPTURE MODE ===== */
        .reveal.pdf-capture-mode .slides section {
            transform: none !important;
            position: relative !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            left: auto !important;
            top: auto !important;
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
            padding: 2rem !important;
            background: white !important;
        }

        .reveal.pdf-capture-mode .slides section.present {
            transform: none !important;
            position: relative !important;
            z-index: 1 !important;
        }

        /* ===== PRINT STYLES FOR PDF EXPORT ===== */
        @media print {
            @page {
                size: A4 landscape;
                margin: 0.5in;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                font-size: 12pt;
                line-height: 1.3;
                color: #000;
                background: white;
            }

            .reveal {
                width: 100% !important;
                height: auto !important;
                overflow: visible !important;
                background: white !important;
            }

            .reveal .slides {
                width: 100% !important;
                height: auto !important;
                left: 0 !important;
                top: 0 !important;
                transform: none !important;
                zoom: 1 !important;
            }

            .reveal .slides section {
                width: 100% !important;
                height: auto !important;
                min-height: 9.5in !important;
                max-height: none !important;
                padding: 0.3in !important;
                margin: 0 !important;
                page-break-after: always !important;
                page-break-inside: avoid !important;
                display: block !important;
                position: relative !important;
                transform: none !important;
                background: white !important;
                box-sizing: border-box !important;
                overflow: visible !important;
            }

            .reveal .slides section:last-child {
                page-break-after: auto !important;
            }

            /* Hide reveal.js controls */
            .reveal .controls,
            .reveal .progress,
            .reveal .playback,
            .reveal .speaker-notes,
            .reveal .pause-overlay {
                display: none !important;
            }

            /* Typography for print */
            .reveal h1 {
                font-size: 18pt !important;
                margin-bottom: 12pt !important;
                color: #6366f1 !important;
                -webkit-text-fill-color: #6366f1 !important;
                background: none !important;
            }

            .reveal h2 {
                font-size: 14pt !important;
                margin-bottom: 10pt !important;
                color: #6366f1 !important;
                border-bottom: 1pt solid #06b6d4 !important;
                padding-bottom: 4pt !important;
            }

            .reveal h3 {
                font-size: 12pt !important;
                margin-bottom: 8pt !important;
                color: #6366f1 !important;
            }

            .reveal p, .reveal li {
                font-size: 10pt !important;
                line-height: 1.3 !important;
                margin-bottom: 6pt !important;
                color: #000 !important;
            }

            /* Title slide adjustments */
            .title-slide {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                text-align: center !important;
                padding: 1in !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                min-height: 9.5in !important;
            }

            .title-slide h1 {
                color: white !important;
                -webkit-text-fill-color: white !important;
                font-size: 24pt !important;
                margin-bottom: 16pt !important;
            }

            .tagline {
                font-size: 14pt !important;
                margin-bottom: 20pt !important;
                color: white !important;
            }

            /* Convert multi-column to single column for print */
            .two-column {
                display: block !important;
                columns: 1 !important;
                column-gap: 0 !important;
            }

            .two-column > div {
                margin-bottom: 12pt !important;
                break-inside: avoid !important;
            }

            /* Feature grid adjustments */
            .feature-grid {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 8pt !important;
                margin: 8pt 0 !important;
            }

            .feature-card {
                padding: 8pt !important;
                margin: 0 !important;
                border: 1pt solid #e2e8f0 !important;
                border-radius: 4pt !important;
                background: #f8fafc !important;
                min-height: auto !important;
                break-inside: avoid !important;
            }

            /* Metric cards for print */
            .metrics-row {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 6pt !important;
                margin: 8pt 0 !important;
            }

            .metric-card {
                flex: 1 !important;
                min-width: 120pt !important;
                padding: 8pt !important;
                margin: 0 !important;
                background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
                color: white !important;
                border-radius: 4pt !important;
                text-align: center !important;
            }

            .metric-value {
                font-size: 14pt !important;
                font-weight: bold !important;
                margin-bottom: 2pt !important;
            }

            .metric-label {
                font-size: 9pt !important;
            }

            /* Financial table for print */
            .financial-table-container {
                overflow: visible !important;
                margin: 8pt 0 !important;
            }

            .financial-table {
                width: 100% !important;
                min-width: auto !important;
                font-size: 9pt !important;
                border-collapse: collapse !important;
            }

            .financial-table th {
                background: #6366f1 !important;
                color: white !important;
                padding: 4pt 6pt !important;
                font-size: 9pt !important;
                border: 1pt solid #6366f1 !important;
            }

            .financial-table td {
                padding: 4pt 6pt !important;
                border: 1pt solid #e2e8f0 !important;
                font-size: 9pt !important;
            }

            /* Highlight boxes */
            .highlight-box {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                padding: 12pt !important;
                border-radius: 6pt !important;
                margin: 8pt 0 !important;
                break-inside: avoid !important;
            }

            .highlight-box h3 {
                color: white !important;
                margin-bottom: 8pt !important;
            }

            /* Bullet points */
            .bullet-point {
                margin: 4pt 0 !important;
                break-inside: avoid !important;
            }

            .bullet-point i {
                color: #10b981 !important;
                margin-right: 6pt !important;
                font-size: 8pt !important;
            }

            /* Tech stack */
            .tech-stack {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 4pt !important;
            }

            .tech-badge {
                background: #ff6b00 !important;
                color: white !important;
                padding: 2pt 6pt !important;
                border-radius: 8pt !important;
                font-size: 8pt !important;
                margin: 1pt !important;
            }

            /* Progress bars */
            .progress-bar {
                height: 8pt !important;
                background: #e2e8f0 !important;
                border-radius: 4pt !important;
                margin: 4pt 0 !important;
            }

            .progress-fill {
                background: linear-gradient(90deg, #10b981, #06b6d4) !important;
                height: 100% !important;
                border-radius: 4pt !important;
            }

            /* CTA buttons */
            .cta-button {
                display: inline-block !important;
                background: linear-gradient(135deg, #ff6b00, #333333) !important;
                color: white !important;
                padding: 6pt 12pt !important;
                border-radius: 4pt !important;
                text-decoration: none !important;
                font-size: 9pt !important;
                margin: 4pt !important;
            }

            /* Add slide numbers */
            .reveal .slides section::before {
                content: counter(slide-counter);
                counter-increment: slide-counter;
                position: absolute;
                top: 0.2in;
                right: 0.2in;
                font-size: 10pt;
                color: #6b7280;
                font-weight: bold;
            }

            .reveal .slides {
                counter-reset: slide-counter;
            }

            /* Ensure icons print */
            .fas, .far, .fab {
                font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
                font-weight: 900 !important;
            }
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- Slide 1: Title -->
            <section class="title-slide">
                <h1><i class="fas fa-trophy"></i> Vibtrix</h1>
                <p class="tagline">The Future of Social Media Competitions</p>
                <p style="font-size: 1.1em; margin-bottom: 40px;">
                    Secure • Engaging • Profitable
                </p>
                <div style="margin-top: 50px;">
                    <p><strong>Presented by:</strong> RekTech Team</p>
                    <p><strong>Date:</strong> July 2025</p>
                </div>
            </section>

            <!-- Slide 2: Project Overview -->
            <section>
                <h2><i class="fas fa-lightbulb"></i> Project Overview</h2>
                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;">What is Vibtrix?</h3>
                    <p style="font-size: 1.2em; line-height: 1.6;">
                        A revolutionary social media platform that gamifies content creation through competitive challenges, 
                        combining the engagement of TikTok with the competitive spirit of esports.
                    </p>
                </div>
                
                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-target"></i> Problem We Solve</h3>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Content creators struggle to monetize their skills</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Lack of structured competition in social media</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Limited earning opportunities for emerging creators</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Security concerns in existing platforms</span>
                        </div>
                    </div>
                    
                    <div>
                        <h3><i class="fas fa-star"></i> Our Solution</h3>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span>Multi-round competitive challenges</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span>Monetized entry fees and prize pools</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-shield-alt"></i>
                            <span>Enterprise-grade security (100% OWASP compliant)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Cross-platform accessibility</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 3: Market Opportunity -->
            <section>
                <h2><i class="fas fa-chart-line"></i> Market Opportunity</h2>
                
                <div class="metrics-row">
                    <div class="metric-card">
                        <div class="metric-value">₹12,25,000Cr</div>
                        <div class="metric-label">Global Social Media Market (2024)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">₹1,50,000Cr</div>
                        <div class="metric-label">Creator Economy Market</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">50M+</div>
                        <div class="metric-label">Content Creators Worldwide</div>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-users"></i> Target Demographics</h3>
                        <div class="bullet-point">
                            <i class="fas fa-user"></i>
                            <span><strong>Primary:</strong> Ages 16-35, content creators</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-user"></i>
                            <span><strong>Secondary:</strong> Brands seeking influencer partnerships</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-user"></i>
                            <span><strong>Geographic:</strong> India, Southeast Asia, Global expansion</span>
                        </div>
                        
                        <h3 style="margin-top: 30px;"><i class="fas fa-crosshairs"></i> Market Size</h3>
                        <div class="bullet-point">
                            <i class="fas fa-chart-pie"></i>
                            <span><strong>TAM:</strong> ₹4,17,000Cr (Global social media)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-pie"></i>
                            <span><strong>SAM:</strong> ₹41,700Cr (Competitive social platforms)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-pie"></i>
                            <span><strong>SOM:</strong> ₹4,170Cr (Our addressable market)</span>
                        </div>
                    </div>
                    
                    <div>
                        <h3><i class="fas fa-fire"></i> Market Trends</h3>
                        <div class="bullet-point">
                            <i class="fas fa-arrow-up"></i>
                            <span>Creator economy growing 23% annually</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-arrow-up"></i>
                            <span>Gamification market: ₹2,50,000Cr by 2026</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-arrow-up"></i>
                            <span>Mobile-first content consumption</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-arrow-up"></i>
                            <span>Increased demand for authentic content</span>
                        </div>
                        
                        <h3 style="margin-top: 30px;"><i class="fas fa-trophy"></i> Competitive Advantage</h3>
                        <div class="bullet-point">
                            <i class="fas fa-shield"></i>
                            <span>First-mover in competitive social media</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-lock"></i>
                            <span>Unmatched security infrastructure</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span>Built-in monetization from day one</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 4: Indian Market Opportunity -->
            <section>
                <h2><i class="fas fa-map-marked-alt"></i> Indian Market Opportunity</h2>

                <div class="metrics-row">
                    <div class="metric-card">
                        <div class="metric-value">700M+</div>
                        <div class="metric-label">Internet Users in India</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">₹2,00,000Cr</div>
                        <div class="metric-label">Indian Digital Economy (2024)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">85%</div>
                        <div class="metric-label">Mobile Internet Usage</div>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-chart-line"></i> Market Dynamics</h3>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>Young Demographics:</strong> 65% population under 35 years</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-mobile-alt"></i>
                            <span><strong>Mobile-First:</strong> 99% internet access via smartphones</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-video"></i>
                            <span><strong>Content Consumption:</strong> 4.5 hours daily video content</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rupee-sign"></i>
                            <span><strong>Digital Payments:</strong> UPI transactions: ₹1,50,00,000Cr annually</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-language"></i> Localization Strategy</h3>
                        <div class="bullet-point">
                            <i class="fas fa-globe"></i>
                            <span>Multi-language support (Hindi, English, Regional)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span>Cultural events and festival-based competitions</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-trophy"></i> Competitive Landscape</h3>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span><strong>Instagram:</strong> 230M users, limited competition features</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span><strong>YouTube:</strong> 467M users, no direct competition platform</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span><strong>TikTok Ban:</strong> Market gap for short-form competitive content</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-bullseye"></i>
                            <span><strong>Our Advantage:</strong> First dedicated competition platform</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-rocket"></i> Growth Potential</h3>
                        <div class="bullet-point">
                            <i class="fas fa-chart-bar"></i>
                            <span><strong>Creator Economy:</strong> ₹23,300Cr market by 2025</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-gamepad"></i>
                            <span><strong>Gaming Market:</strong> ₹62,500Cr, 420M gamers</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span><strong>Digital Tipping:</strong> ₹4,170Cr annual market</span>
                        </div>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-flag"></i> India-First Strategy</h3>
                    <p style="color: white; font-size: 1.2rem; margin: 0;">
                        Vibtrix is designed with Indian market preferences in mind: mobile-first architecture,
                        integrated payment systems (UPI, Paytm), regional language support, and cultural
                        sensitivity. Our platform addresses the unique needs of Indian content creators
                        while providing global scalability.
                    </p>
                </div>
            </section>

            <!-- Slide 5: Product Demo/Features -->
            <section>
                <h2><i class="fas fa-cogs"></i> Core Features & Technology</h2>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-trophy"></i></div>
                        <h4>Competition System</h4>
                        <p>Multi-round competitions with qualification requirements, entry fees, and prize distribution</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                        <h4>Enterprise Security</h4>
                        <p>100% OWASP compliant, rate limiting, JWT authentication, SQL injection prevention</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-credit-card"></i></div>
                        <h4>Payment Integration</h4>
                        <p>Razorpay integration for entry fees, automated prize distribution, QR code payments</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
                        <h4>Mobile-First API</h4>
                        <p>Complete REST API with JWT tokens, optimized for mobile app development</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-comments"></i></div>
                        <h4>Social Features</h4>
                        <p>Real-time messaging, notifications, likes, comments, following system</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-chart-bar"></i></div>
                        <h4>Admin Dashboard</h4>
                        <p>Comprehensive admin panel for managing competitions, users, and content moderation</p>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-code"></i> Technical Stack</h3>
                    <div class="tech-stack">
                        <span class="tech-badge">Next.js 15</span>
                        <span class="tech-badge">React 19</span>
                        <span class="tech-badge">TypeScript</span>
                        <span class="tech-badge">Prisma ORM</span>
                        <span class="tech-badge">PostgreSQL</span>
                        <span class="tech-badge">Lucia Auth</span>
                        <span class="tech-badge">TailwindCSS</span>
                        <span class="tech-badge">Razorpay</span>
                        <span class="tech-badge">Firebase</span>
                        <span class="tech-badge">UploadThing</span>
                    </div>
                </div>
            </section>

            <!-- Slide 6: Current Status -->
            <section>
                <h2><i class="fas fa-rocket"></i> Development Status & Milestones</h2>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-check-circle"></i> Completed Features</h3>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Full-stack web application (100%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Competition management system (100%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Payment integration (100%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Security implementation (100%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Admin dashboard (100%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Mobile API (100%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check" style="color: var(--success-color);"></i>
                            <span>Real-time features (100%)</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-code-branch"></i> Technical Achievements</h3>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span>1,000+ lines of comprehensive API documentation</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span>40+ database tables with complex relationships</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span>100+ API endpoints implemented</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span>Enterprise-grade security measures</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-chart-line"></i> Development Progress</h3>

                        <div style="margin: 20px 0;">
                            <p><strong>Backend Development</strong></p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%;"></div>
                            </div>
                            <small>100% Complete</small>
                        </div>

                        <div style="margin: 20px 0;">
                            <p><strong>Frontend Development</strong></p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 95%;"></div>
                            </div>
                            <small>95% Complete</small>
                        </div>

                        <div style="margin: 20px 0;">
                            <p><strong>Security Implementation</strong></p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%;"></div>
                            </div>
                            <small>100% Complete</small>
                        </div>

                        <div style="margin: 20px 0;">
                            <p><strong>Payment Integration</strong></p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%;"></div>
                            </div>
                            <small>100% Complete</small>
                        </div>

                        <div style="margin: 20px 0;">
                            <p><strong>Mobile API</strong></p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%;"></div>
                            </div>
                            <small>100% Complete</small>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-calendar-alt"></i> Recent Milestones</h3>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q2 2024:</strong> Core platform development</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q3 2024:</strong> Security implementation</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q4 2024:</strong> Payment integration</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q1 2025:</strong> Mobile API completion</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 7: Vision & Roadmap -->
            <section>
                <h2><i class="fas fa-map-marked-alt"></i> Vision & Roadmap</h2>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-eye"></i> Our Vision</h3>
                    <p style="font-size: 1.3em; line-height: 1.6;">
                        To become the world's leading platform for competitive content creation,
                        empowering millions of creators to monetize their talents while building
                        a secure, engaging community.
                    </p>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-calendar-check"></i> 2025 Roadmap</h3>
                        <div class="bullet-point">
                            <i class="fas fa-mobile"></i>
                            <span><strong>Q2:</strong> Mobile app launch (iOS & Android)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>Q2:</strong> Beta testing with 1,000 users</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rocket"></i>
                            <span><strong>Q3:</strong> Public launch in India</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-handshake"></i>
                            <span><strong>Q4:</strong> Brand partnership program</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-globe"></i> 2026-2027 Expansion</h3>
                        <div class="bullet-point">
                            <i class="fas fa-map"></i>
                            <span>Southeast Asia market entry</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-language"></i>
                            <span>Multi-language support</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span>Cryptocurrency integration</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-robot"></i>
                            <span>AI-powered content recommendations</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-chart-line"></i> Growth Strategy</h3>
                        <div class="bullet-point">
                            <i class="fas fa-bullhorn"></i>
                            <span><strong>Influencer Marketing:</strong> Partner with top creators</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-graduation-cap"></i>
                            <span><strong>Educational Content:</strong> Creator skill development</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span><strong>Major Competitions:</strong> High-prize tournaments</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-building"></i>
                            <span><strong>B2B Partnerships:</strong> Corporate competitions</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-target"></i> Key Metrics Goals</h3>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>2025:</strong> 100K active users</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>2026:</strong> 1M active users</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rupee-sign"></i>
                            <span><strong>2025:</strong> ₹8.3Cr ARR</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rupee-sign"></i>
                            <span><strong>2027:</strong> ₹417Cr ARR</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 8: Business Model -->
            <section>
                <h2><i class="fas fa-business-time"></i> Business Model</h2>

                <div class="metrics-row">
                    <div class="metric-card">
                        <div class="metric-value">40%</div>
                        <div class="metric-label">Competition Entry Fees</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">25%</div>
                        <div class="metric-label">Brand Partnerships</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">20%</div>
                        <div class="metric-label">Premium Features</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">15%</div>
                        <div class="metric-label">Advertising Revenue</div>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-coins"></i> Revenue Streams</h3>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span><strong>Competition Fees:</strong> 10-15% commission on entry fees</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-handshake"></i>
                            <span><strong>Brand Partnerships:</strong> Sponsored competitions and content</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span><strong>Premium Subscriptions:</strong> Advanced features and analytics</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-ad"></i>
                            <span><strong>Advertising:</strong> Targeted ads and promoted content</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-graduation-cap"></i>
                            <span><strong>Creator Education:</strong> Courses and workshops</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-chart-pie"></i> Unit Economics</h3>
                        <div class="bullet-point">
                            <i class="fas fa-user"></i>
                            <span><strong>CAC:</strong> ₹1,250 (Customer Acquisition Cost)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rupee-sign"></i>
                            <span><strong>LTV:</strong> ₹15,000 (Lifetime Value)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calculator"></i>
                            <span><strong>LTV/CAC Ratio:</strong> 12:1</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-clock"></i>
                            <span><strong>Payback Period:</strong> 3 months</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-users"></i> Target Customer Segments</h3>
                        <div class="bullet-point">
                            <i class="fas fa-video"></i>
                            <span><strong>Content Creators:</strong> Aspiring and established influencers</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-building"></i>
                            <span><strong>Brands:</strong> Companies seeking authentic marketing</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-eye"></i>
                            <span><strong>Viewers:</strong> Entertainment-seeking audience</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-graduation-cap"></i>
                            <span><strong>Agencies:</strong> Talent management companies</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-rocket"></i> Competitive Advantages</h3>
                        <div class="bullet-point">
                            <i class="fas fa-shield"></i>
                            <span><strong>Security First:</strong> Enterprise-grade protection</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span><strong>Gamification:</strong> Unique competitive format</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span><strong>Monetization:</strong> Built-in earning opportunities</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-mobile"></i>
                            <span><strong>Mobile-First:</strong> Optimized for mobile experience</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 9: Financial Projections -->
            <section>
                <h2><i class="fas fa-chart-bar"></i> Financial Projections</h2>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-trending-up"></i> 5-Year Revenue Forecast</h3>
                    <p style="font-size: 1.2em;">Conservative projections based on market analysis and comparable platforms</p>
                </div>

                <div class="financial-table-container">
                    <table class="financial-table">
                    <thead>
                        <tr>
                            <th>Year</th>
                            <th>Users</th>
                            <th>Revenue</th>
                            <th>Gross Margin</th>
                            <th>Net Income</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>2025</strong></td>
                            <td>100,000</td>
                            <td>₹10Cr</td>
                            <td>75%</td>
                            <td>-₹4.2Cr</td>
                        </tr>
                        <tr>
                            <td><strong>2026</strong></td>
                            <td>500,000</td>
                            <td>₹71Cr</td>
                            <td>78%</td>
                            <td>₹10Cr</td>
                        </tr>
                        <tr>
                            <td><strong>2027</strong></td>
                            <td>1,500,000</td>
                            <td>₹208Cr</td>
                            <td>80%</td>
                            <td>₹50Cr</td>
                        </tr>
                        <tr>
                            <td><strong>2028</strong></td>
                            <td>3,000,000</td>
                            <td>₹417Cr</td>
                            <td>82%</td>
                            <td>₹125Cr</td>
                        </tr>
                        <tr>
                            <td><strong>2029</strong></td>
                            <td>5,000,000</td>
                            <td>₹708Cr</td>
                            <td>85%</td>
                            <td>₹250Cr</td>
                        </tr>
                    </tbody>
                </table>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-calculator"></i> Key Assumptions</h3>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span>User growth: 150% annually (Years 1-3)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rupee-sign"></i>
                            <span>ARPU: ₹1,000-1,400 (increasing over time)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-percentage"></i>
                            <span>Conversion rate: 15% (freemium to paid)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-line"></i>
                            <span>Churn rate: 5% monthly (improving to 3%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span>Competition participation: 25% of users</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-bullseye"></i> Revenue Breakdown (2027)</h3>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span><strong>Competition Fees:</strong> ₹83Cr (40%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-handshake"></i>
                            <span><strong>Brand Partnerships:</strong> ₹52Cr (25%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-star"></i>
                            <span><strong>Premium Features:</strong> ₹42Cr (20%)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-ad"></i>
                            <span><strong>Advertising:</strong> ₹31Cr (15%)</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-chart-pie"></i> Market Validation</h3>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Similar platforms: ₹417-834Cr ARR</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Creator economy: 23% annual growth</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span>Mobile gaming: ₹8,34,000Cr+ market</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 10: Funding Request -->
            <section>
                <h2><i class="fas fa-money-bill-wave"></i> Funding Request</h2>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-rocket"></i> Seeking ₹21Cr Series A</h3>
                    <p style="font-size: 1.3em; line-height: 1.6;">
                        To accelerate growth, expand our team, and capture the competitive social media market
                    </p>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-handshake"></i> Investment Terms</h3>
                        <div class="bullet-point">
                            <i class="fas fa-dollar-sign"></i>
                            <span><strong>Amount:</strong> $2.5M Series A</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-pie"></i>
                            <span><strong>Equity:</strong> 20% (Pre-money: $10M)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Timeline:</strong> 18-month runway</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-target"></i>
                            <span><strong>Milestones:</strong> 100K users, $1M ARR</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-trophy"></i> Why Invest Now?</h3>
                        <div class="bullet-point">
                            <i class="fas fa-clock"></i>
                            <span><strong>First Mover:</strong> Unique market position</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-rocket"></i>
                            <span><strong>Proven Tech:</strong> Platform ready for scale</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-line"></i>
                            <span><strong>Market Timing:</strong> Creator economy boom</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-shield"></i>
                            <span><strong>Competitive Moat:</strong> Security & features</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-chart-line"></i> Investor Returns</h3>
                        <div class="bullet-point">
                            <i class="fas fa-percentage"></i>
                            <span><strong>Projected IRR:</strong> 45-60%</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-times"></i>
                            <span><strong>Multiple:</strong> 15-25x (5-year exit)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-building"></i>
                            <span><strong>Exit Strategy:</strong> Strategic acquisition</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>Comparable Exits:</strong> $500M - $2B</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-shield-alt"></i> Risk Mitigation</h3>
                        <div class="bullet-point">
                            <i class="fas fa-code"></i>
                            <span><strong>Technical:</strong> Proven, scalable platform</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>Market:</strong> Large, growing addressable market</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span><strong>Revenue:</strong> Multiple monetization streams</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-team"></i>
                            <span><strong>Team:</strong> Experienced technical leadership</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 11: Use of Funds -->
            <section>
                <h2><i class="fas fa-chart-pie"></i> Use of Funds</h2>

                <div class="metrics-row">
                    <div class="metric-card">
                        <div class="metric-value">40%</div>
                        <div class="metric-label">Product Development</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">30%</div>
                        <div class="metric-label">Marketing & Growth</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">20%</div>
                        <div class="metric-label">Team Expansion</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">10%</div>
                        <div class="metric-label">Operations & Legal</div>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-code"></i> Product Development (40% - $1M)</h3>
                        <div class="bullet-point">
                            <i class="fas fa-mobile"></i>
                            <span>Mobile app development (iOS & Android)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-robot"></i>
                            <span>AI-powered recommendation engine</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-video"></i>
                            <span>Advanced video editing tools</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-bar"></i>
                            <span>Analytics and creator insights</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-globe"></i>
                            <span>Multi-language localization</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-bullhorn"></i> Marketing & Growth (30% - $750K)</h3>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span>Influencer partnerships and campaigns</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-ad"></i>
                            <span>Digital advertising (Google, Meta, TikTok)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span>Launch competitions and prize pools</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-handshake"></i>
                            <span>Brand partnership development</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-users"></i> Team Expansion (20% - $500K)</h3>
                        <div class="bullet-point">
                            <i class="fas fa-mobile"></i>
                            <span>Mobile developers (2 positions)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-paint-brush"></i>
                            <span>UI/UX designers (2 positions)</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-chart-line"></i>
                            <span>Growth marketing manager</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-headset"></i>
                            <span>Community manager</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-shield"></i>
                            <span>DevOps engineer</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-building"></i> Operations & Legal (10% - $250K)</h3>
                        <div class="bullet-point">
                            <i class="fas fa-gavel"></i>
                            <span>Legal compliance and IP protection</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-server"></i>
                            <span>Infrastructure and hosting</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-shield-alt"></i>
                            <span>Security audits and certifications</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-coins"></i>
                            <span>Financial systems and accounting</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 12: Team -->
            <section>
                <h2><i class="fas fa-users"></i> Our Team</h2>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-star"></i> Experienced Leadership</h3>
                    <p style="font-size: 1.2em;">A proven team with deep expertise in social media, security, and scalable platforms</p>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-user-tie"></i> Core Team</h3>
                        <div style="background: #f8fafc; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4><i class="fas fa-crown"></i> CEO & Founder</h4>
                            <p><strong>RekTech Team Lead</strong></p>
                            <div class="bullet-point">
                                <i class="fas fa-graduation-cap"></i>
                                <span>10+ years in software development</span>
                            </div>
                            <div class="bullet-point">
                                <i class="fas fa-rocket"></i>
                                <span>Previous startup experience</span>
                            </div>
                            <div class="bullet-point">
                                <i class="fas fa-shield"></i>
                                <span>Security and scalability expert</span>
                            </div>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4><i class="fas fa-code"></i> CTO</h4>
                            <p><strong>[To be hired]</strong></p>
                            <div class="bullet-point">
                                <i class="fas fa-mobile"></i>
                                <span>Mobile app development expertise</span>
                            </div>
                            <div class="bullet-point">
                                <i class="fas fa-cloud"></i>
                                <span>Cloud infrastructure experience</span>
                            </div>
                            <div class="bullet-point">
                                <i class="fas fa-users"></i>
                                <span>Team leadership background</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-handshake"></i> Advisory Board</h3>
                        <div style="background: #f8fafc; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4><i class="fas fa-chart-line"></i> Growth Advisor</h4>
                            <p><strong>[To be recruited]</strong></p>
                            <div class="bullet-point">
                                <i class="fas fa-rocket"></i>
                                <span>Social media growth expertise</span>
                            </div>
                            <div class="bullet-point">
                                <i class="fas fa-users"></i>
                                <span>Creator economy experience</span>
                            </div>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4><i class="fas fa-dollar-sign"></i> Business Advisor</h4>
                            <p><strong>[To be recruited]</strong></p>
                            <div class="bullet-point">
                                <i class="fas fa-building"></i>
                                <span>Enterprise partnerships</span>
                            </div>
                            <div class="bullet-point">
                                <i class="fas fa-coins"></i>
                                <span>Monetization strategies</span>
                            </div>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-target"></i> Hiring Plan</h3>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q2 2025:</strong> CTO and 2 mobile developers</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q3 2025:</strong> Growth marketer and designers</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-calendar"></i>
                            <span><strong>Q4 2025:</strong> Community and operations team</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Slide 13: Next Steps -->
            <section>
                <h2><i class="fas fa-forward"></i> Next Steps</h2>

                <div class="highlight-box">
                    <h3 style="color: white; margin-bottom: 20px;"><i class="fas fa-rocket"></i> Ready to Launch</h3>
                    <p style="font-size: 1.3em; line-height: 1.6;">
                        Platform is built, tested, and ready for scale. We're seeking the right partners to accelerate our growth.
                    </p>
                </div>

                <div class="two-column">
                    <div>
                        <h3><i class="fas fa-calendar-check"></i> Immediate Milestones</h3>
                        <div class="bullet-point">
                            <i class="fas fa-handshake"></i>
                            <span><strong>Week 1-2:</strong> Investor meetings and due diligence</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-pen"></i>
                            <span><strong>Week 3-4:</strong> Term sheet negotiation</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-check"></i>
                            <span><strong>Month 2:</strong> Funding close and team expansion</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-mobile"></i>
                            <span><strong>Month 3:</strong> Mobile app development start</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span><strong>Month 4:</strong> Beta testing program launch</span>
                        </div>

                        <h3 style="margin-top: 30px;"><i class="fas fa-trophy"></i> Success Metrics</h3>
                        <div class="bullet-point">
                            <i class="fas fa-users"></i>
                            <span>100K registered users by end of 2025</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$1M ARR by Q4 2025</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-trophy"></i>
                            <span>1,000+ competitions hosted</span>
                        </div>
                        <div class="bullet-point">
                            <i class="fas fa-handshake"></i>
                            <span>50+ brand partnerships</span>
                        </div>
                    </div>

                    <div>
                        <h3><i class="fas fa-phone"></i> Contact Information</h3>
                        <div style="background: #f8fafc; padding: 25px; border-radius: 10px; text-align: center;">
                            <h4 style="color: var(--primary-color); margin-bottom: 20px;">Let's Build the Future Together</h4>
                            <div class="bullet-point">
                               
                                <span><strong>Email:</strong> <EMAIL></span>
                            </div>
                            <div class="bullet-point">
                               
                                <span><strong>Website:</strong> vibtrix.com</span>
                            </div>

                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button onclick="downloadPDF()" class="cta-button" id="pdfButton">
                                <i class="fas fa-file-pdf"></i> Save as PDF
                            </button>
                        </div>

                        <div style="text-align: center; margin-top: 12px; padding: 10px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 6px; color: white;">
                            <h4 style="color: white; margin-bottom: 6px; font-size: 0.85em;">
                                <i class="fas fa-star"></i> Thank You
                            </h4>
                            <p style="font-size: 0.8em;color: white;">
                                Ready to revolutionize social media together?
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        // Global flag to track Reveal.js readiness
        let revealReady = false;

        // Fallback if Reveal.js fails to load
        window.addEventListener('load', function() {
            if (typeof Reveal === 'undefined') {
                console.log('Reveal.js failed to load, showing static content');
                document.querySelector('.reveal .slides').style.display = 'block';
                return;
            }
        });

        // Initialize Reveal.js with proper event handling
        Reveal.initialize({
            hash: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            center: false,
            touch: true,
            loop: false,
            rtl: false,
            navigationMode: 'default',
            shuffle: false,
            fragments: true,
            fragmentInURL: false,
            embedded: false,
            help: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoSlide: 0,
            autoSlideStoppable: true,
            autoSlideMethod: 'default',
            defaultTiming: null,
            mouseWheel: false,
            hideInactiveCursor: true,
            hideCursorTime: 5000,
            previewLinks: false,
            postMessage: true,
            postMessageEvents: false,
            focusBodyOnPageVisibilityChange: true,
            viewDistance: 3,
            mobileViewDistance: 2,
            display: 'block',
            hideAddressBar: true,
            width: '100%',
            height: '100%',
            margin: 0.01,
            minScale: 0.4,
            maxScale: 1.2,
            disableLayout: false,
            parallaxBackgroundImage: '',
            parallaxBackgroundSize: '',
            parallaxBackgroundRepeat: '',
            parallaxBackgroundPosition: '',
            parallaxBackgroundHorizontal: null,
            parallaxBackgroundVertical: null
        }).then(() => {
            console.log('Reveal.js initialized successfully');
            revealReady = true;
        });

        // Enhanced PDF Download Function
        function downloadPDF() {
            const button = document.getElementById('pdfButton');
            if (!button) {
                console.error('PDF button not found');
                return;
            }

            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
            button.disabled = true;

            // Check if required libraries are loaded
            if (typeof window.jspdf === 'undefined') {
                console.error('jsPDF library not loaded');
                alert('PDF generation library not loaded. Please refresh the page and try again.');
                button.innerHTML = originalText;
                button.disabled = false;
                return;
            }

            if (typeof html2canvas === 'undefined') {
                console.error('html2canvas library not loaded');
                alert('Canvas rendering library not loaded. Falling back to print method.');
                fallbackPrintPDF();
                return;
            }

            // Check if Reveal.js is ready
            if (typeof Reveal === 'undefined' || !revealReady) {
                console.error('Reveal.js not ready');
                alert('Presentation framework not ready. Please wait a moment and try again.');
                button.innerHTML = originalText;
                button.disabled = false;
                return;
            }

            console.log('Starting PDF generation...');

            // Use high-quality canvas-based PDF generation
            generateCanvasPDF().then(() => {
                console.log('PDF generation completed successfully');
                // Reset button
                button.innerHTML = originalText;
                button.disabled = false;
            }).catch((error) => {
                console.error('Canvas-based PDF generation failed:', error);
                console.log('Falling back to print-based PDF generation');

                // Show user-friendly error message
                button.innerHTML = '<i class="fas fa-print"></i> Using Print Method...';

                // Fallback to print method
                try {
                    fallbackPrintPDF();
                } catch (fallbackError) {
                    console.error('Print-based PDF generation also failed:', fallbackError);
                    alert('PDF generation failed. Please try using your browser\'s print function (Ctrl+P or Cmd+P) and select "Save as PDF".');
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            });
        }

        // Simplified and reliable PDF generation
        async function generateCanvasPDF() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('l', 'mm', 'a4'); // landscape A4

            console.log('Starting simplified PDF generation...');

            // Hide the PDF button during generation
            const pdfButton = document.getElementById('pdfButton');
            const originalButtonDisplay = pdfButton ? pdfButton.style.display : '';
            if (pdfButton) {
                pdfButton.style.display = 'none';
            }

            try {
                // Create a properly sized container for PDF generation
                // A4 landscape: 297mm x 210mm ≈ 1.41:1 aspect ratio
                // Use dimensions that match this ratio for better quality
                const slidesContainer = document.createElement('div');
                slidesContainer.style.position = 'absolute';
                slidesContainer.style.left = '-9999px';
                slidesContainer.style.top = '0';
                slidesContainer.style.width = '1410px';  // Proper A4 landscape ratio
                slidesContainer.style.height = '1000px'; // Maintains 1.41:1 ratio
                slidesContainer.style.background = '#ffffff';
                slidesContainer.style.fontFamily = '"Inter", "Helvetica Neue", sans-serif';
                slidesContainer.style.fontSize = '28px';  // Larger for better PDF quality
                slidesContainer.style.lineHeight = '1.4';
                slidesContainer.style.color = '#333333';
                slidesContainer.style.boxSizing = 'border-box';

                // Get all slide content as simple HTML
                const slides = document.querySelectorAll('.reveal .slides section');
                console.log(`Found ${slides.length} slides to process`);

                if (slides.length === 0) {
                    throw new Error('No slides found to generate PDF');
                }

                document.body.appendChild(slidesContainer);

                for (let i = 0; i < slides.length; i++) {
                    console.log(`Processing slide ${i + 1} of ${slides.length}`);

                    // Clear container and set proper dimensions
                    slidesContainer.innerHTML = '';
                    slidesContainer.style.padding = '60px';  // Proportional padding
                    slidesContainer.style.overflow = 'hidden';

                    // Get slide content
                    const slide = slides[i];
                    const slideContent = slide.innerHTML;

                    // Create a clean version without buttons
                    let cleanContent = slideContent;
                    cleanContent = cleanContent.replace(/<button[^>]*>.*?<\/button>/gi, '');
                    cleanContent = cleanContent.replace(/id="pdfButton"/gi, '');
                    cleanContent = cleanContent.replace(/class="cta-button"/gi, '');

                    // Create a properly styled container that preserves original layout
                    const slideWrapper = document.createElement('div');
                    slideWrapper.style.cssText = `
                        width: 100%;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        text-align: left;
                        padding: 40px;
                        box-sizing: border-box;
                        background: white;
                        font-family: "Inter", "Helvetica Neue", sans-serif;
                        line-height: 1.4;
                        color: #333333;
                    `;

                    // Add the cleaned content
                    slideWrapper.innerHTML = cleanContent;

                    // Apply original slide styles to preserve formatting
                    const originalSlideStyles = window.getComputedStyle(slide);

                    // Copy important styles from original slide
                    if (originalSlideStyles.textAlign) {
                        slideWrapper.style.textAlign = originalSlideStyles.textAlign;
                    }

                    // Enhance text rendering
                    slideWrapper.style.webkitFontSmoothing = 'antialiased';
                    slideWrapper.style.mozOsxFontSmoothing = 'grayscale';
                    slideWrapper.style.textRendering = 'optimizeLegibility';

                    slidesContainer.appendChild(slideWrapper);

                    // Apply slide-specific styling based on original slide classes
                    if (slide.classList.contains('title-slide')) {
                        slideWrapper.style.background = 'linear-gradient(135deg, #ff6b00 0%, #333333 100%)';
                        slideWrapper.style.color = 'white';
                        slideWrapper.style.textAlign = 'center';

                        // Enhance title slide typography
                        const h1Elements = slideWrapper.querySelectorAll('h1');
                        h1Elements.forEach(h1 => {
                            h1.style.fontSize = '3.5em';
                            h1.style.fontWeight = 'bold';
                            h1.style.marginBottom = '0.5em';
                            h1.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
                        });

                        const pElements = slideWrapper.querySelectorAll('p');
                        pElements.forEach(p => {
                            p.style.fontSize = '1.4em';
                            p.style.marginBottom = '1em';
                        });
                    } else {
                        // Regular content slides
                        slideWrapper.style.textAlign = 'left';

                        // Enhance regular slide typography
                        const h2Elements = slideWrapper.querySelectorAll('h2');
                        h2Elements.forEach(h2 => {
                            h2.style.fontSize = '2.5em';
                            h2.style.fontWeight = 'bold';
                            h2.style.marginBottom = '0.8em';
                            h2.style.color = '#ff6b00';
                        });

                        const h3Elements = slideWrapper.querySelectorAll('h3');
                        h3Elements.forEach(h3 => {
                            h3.style.fontSize = '1.8em';
                            h3.style.fontWeight = '600';
                            h3.style.marginBottom = '0.6em';
                            h3.style.color = '#333333';
                        });

                        const pElements = slideWrapper.querySelectorAll('p');
                        pElements.forEach(p => {
                            p.style.fontSize = '1.2em';
                            p.style.lineHeight = '1.6';
                            p.style.marginBottom = '1em';
                        });

                        const liElements = slideWrapper.querySelectorAll('li');
                        liElements.forEach(li => {
                            li.style.fontSize = '1.1em';
                            li.style.lineHeight = '1.5';
                            li.style.marginBottom = '0.5em';
                        });
                    }

                    // Ensure all icons render properly
                    const iconElements = slideWrapper.querySelectorAll('.fas, .fab, .far');
                    iconElements.forEach(icon => {
                        icon.style.fontFamily = '"Font Awesome 6 Free"';
                        icon.style.fontWeight = '900';
                        icon.style.display = 'inline-block';
                        icon.style.fontSize = '1em';
                    });

                    // Fix any tables or structured content
                    const tables = slideWrapper.querySelectorAll('table');
                    tables.forEach(table => {
                        table.style.width = '100%';
                        table.style.borderCollapse = 'collapse';
                        table.style.fontSize = '1em';
                    });

                    // Enhance bullet points and lists
                    const ulElements = slideWrapper.querySelectorAll('ul');
                    ulElements.forEach(ul => {
                        ul.style.paddingLeft = '1.5em';
                        ul.style.marginBottom = '1em';
                    });

                    // Fix any highlight boxes or special containers
                    const highlightBoxes = slideWrapper.querySelectorAll('.highlight-box, .bullet-point');
                    highlightBoxes.forEach(box => {
                        box.style.padding = '1em';
                        box.style.marginBottom = '1em';
                        box.style.borderRadius = '8px';
                        if (box.classList.contains('highlight-box')) {
                            box.style.background = 'linear-gradient(135deg, #ff6b00, #333333)';
                            box.style.color = 'white';
                        }
                    });

                    // Ensure proper spacing for all elements
                    const allElements = slideWrapper.querySelectorAll('*');
                    allElements.forEach(el => {
                        // Prevent text from being cut off
                        el.style.wordWrap = 'break-word';
                        el.style.overflowWrap = 'break-word';
                    });

                    // Force layout and wait for fonts to load
                    slidesContainer.offsetHeight;

                    // Wait for fonts and images to load properly
                    await document.fonts.ready;
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    try {
                        const canvas = await html2canvas(slidesContainer, {
                            scale: 2,      // Higher scale for crisp text
                            useCORS: true,
                            allowTaint: true,
                            backgroundColor: '#ffffff',
                            width: 1410,   // Match container width
                            height: 1000,  // Match container height
                            scrollX: 0,
                            scrollY: 0,
                            logging: false,
                            letterRendering: true,     // Better text rendering
                            foreignObjectRendering: true,
                            imageTimeout: 15000,       // Wait for images
                            removeContainer: false,    // Keep container for debugging
                            onclone: function(clonedDoc) {
                                // Ensure fonts are loaded in cloned document
                                const style = clonedDoc.createElement('style');
                                style.textContent = `
                                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                                    * {
                                        -webkit-font-smoothing: antialiased !important;
                                        -moz-osx-font-smoothing: grayscale !important;
                                        text-rendering: optimizeLegibility !important;
                                        font-feature-settings: "liga", "kern" !important;
                                    }
                                `;
                                clonedDoc.head.appendChild(style);
                            }
                        });

                        const imgData = canvas.toDataURL('image/png', 1.0);  // Maximum quality

                        if (i > 0) {
                            pdf.addPage();
                        }

                        // Calculate proper dimensions maintaining aspect ratio
                        // A4 landscape: 297mm x 210mm, with 10mm margins = 277mm x 190mm usable
                        const pdfWidth = 277;
                        const pdfHeight = 190;

                        // Center the image properly
                        const marginX = 10;
                        const marginY = 10;

                        pdf.addImage(imgData, 'PNG', marginX, marginY, pdfWidth, pdfHeight);
                        console.log(`Successfully captured slide ${i + 1} with proper scaling`);

                    } catch (error) {
                        console.error(`Error capturing slide ${i + 1}:`, error);

                        // Add error page
                        if (i > 0) {
                            pdf.addPage();
                        }
                        pdf.setFontSize(16);
                        pdf.text(`Error rendering slide ${i + 1}: ${error.message}`, 20, 50);
                    }
                }

                // Clean up
                document.body.removeChild(slidesContainer);

            } finally {
                // Restore PDF button
                if (pdfButton) {
                    pdfButton.style.display = originalButtonDisplay;
                }
            }

            // Download the PDF
            pdf.save('Vibtrix-Pitch-Deck.pdf');
            console.log('PDF generation completed');
        }

        // Simplified fallback print-based PDF generation
        function fallbackPrintPDF() {
            const button = document.getElementById('pdfButton');
            const originalText = button ? button.innerHTML : '';

            console.log('Using print-based PDF fallback');

            // Hide the PDF button immediately
            if (button) {
                button.style.display = 'none';
            }

            // Disable Reveal.js temporarily for printing
            const revealContainer = document.querySelector('.reveal');
            if (revealContainer) {
                revealContainer.classList.add('print-mode');
            }

            // Configure enhanced print settings for PDF
            const printCSS = `
                @media print {
                    @page {
                        size: A4 landscape;
                        margin: 0.3in;
                    }

                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        box-sizing: border-box !important;
                    }

                    body {
                        background: white !important;
                        font-family: "Inter", "Helvetica Neue", sans-serif !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .reveal {
                        background: white !important;
                        width: 100% !important;
                        height: auto !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .reveal .slides {
                        width: 100% !important;
                        height: auto !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .reveal .slides section {
                        page-break-after: always !important;
                        page-break-inside: avoid !important;
                        margin: 0 !important;
                        padding: 15pt !important;
                        width: 100% !important;
                        max-width: none !important;
                        height: auto !important;
                        min-height: 7.5in !important; /* Ensure minimum height for content */
                        overflow: visible !important;
                        background: white !important;
                        display: block !important;
                        flex-direction: column !important;
                        align-items: flex-start !important;
                        position: relative !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        transform: none !important;
                        left: auto !important;
                        top: auto !important;
                    }

                    .reveal .slides section:last-child {
                        page-break-after: auto !important;
                    }

                    /* Force all slides to be visible in print mode */
                    .reveal.print-mode .slides section {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        position: relative !important;
                        transform: none !important;
                    }

                    .title-slide {
                        text-align: center !important;
                        background: linear-gradient(135deg, #ff6b00 0%, #333333 100%) !important;
                        color: white !important;
                        padding: 30pt !important;
                        min-height: auto !important;
                    }

                    .title-slide h1 {
                        color: white !important;
                        font-size: 24pt !important;
                        margin-bottom: 15pt !important;
                    }

                    .tagline {
                        color: white !important;
                        font-size: 14pt !important;
                        margin-bottom: 15pt !important;
                    }

                    h1 {
                        color: #ff6b00 !important;
                        font-size: 20pt !important;
                        font-weight: 700 !important;
                        margin-bottom: 12pt !important;
                    }

                    h2 {
                        color: #333333 !important;
                        font-size: 16pt !important;
                        font-weight: 600 !important;
                        margin-bottom: 10pt !important;
                        border-bottom: 2pt solid #ff6b00 !important;
                        padding-bottom: 3pt !important;
                    }

                    h3 {
                        color: #444444 !important;
                        font-size: 12pt !important;
                        font-weight: 500 !important;
                        margin-bottom: 8pt !important;
                    }

                    p, li {
                        color: #555555 !important;
                        font-size: 10pt !important;
                        line-height: 1.4 !important;
                        margin-bottom: 6pt !important;
                    }

                    .metric-card {
                        background: linear-gradient(135deg, #ff6b00, #333333) !important;
                        color: white !important;
                        padding: 8pt !important;
                        border-radius: 4pt !important;
                        margin: 3pt !important;
                        text-align: center !important;
                        break-inside: avoid !important;
                        display: inline-block !important;
                        min-width: 120pt !important;
                    }

                    .metric-value {
                        font-size: 16pt !important;
                        font-weight: 700 !important;
                        color: white !important;
                        margin-bottom: 3pt !important;
                    }

                    .metric-label {
                        font-size: 9pt !important;
                        color: white !important;
                        opacity: 0.9 !important;
                    }

                    .metrics-row {
                        display: flex !important;
                        flex-wrap: wrap !important;
                        justify-content: space-around !important;
                        margin: 8pt 0 !important;
                        gap: 6pt !important;
                    }

                    .feature-card {
                        background: white !important;
                        border: 1pt solid #e2e8f0 !important;
                        border-radius: 4pt !important;
                        padding: 8pt !important;
                        margin: 3pt !important;
                        text-align: center !important;
                        break-inside: avoid !important;
                        display: inline-block !important;
                        width: 150pt !important;
                        vertical-align: top !important;
                    }

                    .feature-card h4 {
                        color: #ff6b00 !important;
                        font-size: 11pt !important;
                        margin-bottom: 4pt !important;
                    }

                    .feature-card p {
                        font-size: 9pt !important;
                        color: #555555 !important;
                        margin: 0 !important;
                    }

                    .feature-grid {
                        display: flex !important;
                        flex-wrap: wrap !important;
                        justify-content: space-around !important;
                        margin: 8pt 0 !important;
                        gap: 6pt !important;
                    }

                    .highlight-box {
                        background: linear-gradient(135deg, #ff6b00 0%, #333333 100%) !important;
                        color: white !important;
                        padding: 12pt !important;
                        border-radius: 6pt !important;
                        margin: 8pt 0 !important;
                        break-inside: avoid !important;
                    }

                    .highlight-box h3 {
                        color: white !important;
                        font-size: 12pt !important;
                        margin-bottom: 6pt !important;
                    }

                    .highlight-box p {
                        color: white !important;
                        font-size: 10pt !important;
                        margin: 0 !important;
                    }

                    .financial-table-container {
                        margin: 8pt 0 !important;
                        overflow: visible !important;
                    }

                    .financial-table {
                        width: 100% !important;
                        border-collapse: collapse !important;
                        font-size: 9pt !important;
                        margin: 0 !important;
                    }

                    .financial-table th {
                        background: #ff6b00 !important;
                        color: white !important;
                        padding: 6pt 8pt !important;
                        border: 1pt solid #ff6b00 !important;
                        font-size: 9pt !important;
                        font-weight: 600 !important;
                    }

                    .financial-table td {
                        padding: 6pt 8pt !important;
                        border: 1pt solid #e2e8f0 !important;
                        font-size: 9pt !important;
                        color: #555555 !important;
                    }

                    .tech-stack {
                        display: flex !important;
                        flex-wrap: wrap !important;
                        gap: 3pt !important;
                        margin: 6pt 0 !important;
                    }

                    .tech-badge {
                        background: #ff6b00 !important;
                        color: white !important;
                        padding: 3pt 6pt !important;
                        border-radius: 8pt !important;
                        font-size: 8pt !important;
                        display: inline-block !important;
                    }

                    .two-column {
                        display: grid !important;
                        grid-template-columns: 1fr 1fr !important;
                        gap: 12pt !important;
                        margin: 8pt 0 !important;
                    }

                    .bullet-point {
                        display: flex !important;
                        align-items: flex-start !important;
                        margin: 3pt 0 !important;
                    }

                    .bullet-point i {
                        color: #ff6b00 !important;
                        margin-right: 6pt !important;
                        font-size: 9pt !important;
                    }

                    .bullet-point span {
                        font-size: 9pt !important;
                        color: #555555 !important;
                        line-height: 1.3 !important;
                    }

                    .feature-icon {
                        color: #ff6b00 !important;
                        font-size: 18pt !important;
                        margin-bottom: 6pt !important;
                    }

                    .progress-bar {
                        background: #e2e8f0 !important;
                        height: 6pt !important;
                        border-radius: 3pt !important;
                        margin: 6pt 0 !important;
                        overflow: hidden !important;
                    }

                    .progress-fill {
                        background: linear-gradient(90deg, #ff6b00, #333333) !important;
                        height: 100% !important;
                        border-radius: 3pt !important;
                    }

                    /* Hide interactive elements */
                    .cta-button,
                    .reveal .controls,
                    .reveal .progress,
                    #pdfButton {
                        display: none !important;
                    }

                    /* Ensure icons print */
                    .fas, .fab, .far {
                        font-family: "Font Awesome 6 Free" !important;
                        font-weight: 900 !important;
                    }
                }
            `;

            // Add print styles
            const styleSheet = document.createElement('style');
            styleSheet.textContent = printCSS;
            document.head.appendChild(styleSheet);

            // Small delay to ensure styles are applied
            setTimeout(() => {
                // Trigger print dialog
                window.print();

                // Reset button after print dialog
                setTimeout(() => {
                    if (button) {
                        button.innerHTML = originalText;
                        button.disabled = false;
                        button.style.display = ''; // Restore original display
                    }
                    if (document.head.contains(styleSheet)) {
                        document.head.removeChild(styleSheet);
                    }
                    // Remove print-mode class
                    const revealContainer = document.querySelector('.reveal');
                    if (revealContainer) {
                        revealContainer.classList.remove('print-mode');
                    }
                }, 1000);
            }, 500);
        }

        // Debug function to test slide visibility and content
        function debugSlides() {
            const slides = document.querySelectorAll('.reveal .slides section');
            console.log('=== SLIDE DEBUG INFO ===');
            console.log(`Total slides found: ${slides.length}`);

            slides.forEach((slide, index) => {
                const rect = slide.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(slide);
                console.log(`Slide ${index + 1}:`, {
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    opacity: computedStyle.opacity,
                    width: rect.width,
                    height: rect.height,
                    hasContent: slide.textContent.trim().length > 0
                });
            });
            console.log('=== END DEBUG INFO ===');
        }

        // Add debug function to window for manual testing
        window.debugSlides = debugSlides;

        // Add keyboard shortcuts for debugging and testing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                debugSlides();
            }
            // Quality test function (Ctrl+Shift+T)
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                testCanvasCapture();
            }
            // PDF quality validation (Ctrl+Shift+V)
            if (e.ctrlKey && e.shiftKey && e.key === 'V') {
                validatePDFQuality();
            }
        });

        // Quick test to see if html2canvas works at all
        async function testCanvasCapture() {
            console.log('Testing canvas capture...');

            const testDiv = document.createElement('div');
            testDiv.style.position = 'absolute';
            testDiv.style.left = '-9999px';
            testDiv.style.width = '1410px';
            testDiv.style.height = '1000px';
            testDiv.style.background = 'white';
            testDiv.style.color = '#333';
            testDiv.style.padding = '60px';
            testDiv.style.fontFamily = '"Inter", "Helvetica Neue", sans-serif';
            testDiv.style.fontSize = '28px';
            testDiv.style.lineHeight = '1.4';
            testDiv.innerHTML = `
                <h1 style="font-size: 3.5em; color: #ff6b00; margin-bottom: 0.5em;">Test Slide Quality</h1>
                <h2 style="font-size: 2.5em; color: #333; margin-bottom: 0.8em;">Typography Test</h2>
                <p style="font-size: 1.2em; line-height: 1.6; margin-bottom: 1em;">
                    This is a test to verify text quality, scaling, and formatting in the PDF generation.
                    The text should be crisp, properly sized, and maintain good readability.
                </p>
                <ul style="font-size: 1.1em; line-height: 1.5;">
                    <li>Bullet point with proper spacing</li>
                    <li>Another item to test list formatting</li>
                    <li>Final item with <strong>bold text</strong> and <em>italic text</em></li>
                </ul>
                <div style="background: linear-gradient(135deg, #ff6b00, #333333); color: white; padding: 1em; border-radius: 8px; margin-top: 1em;">
                    <h3 style="margin: 0; font-size: 1.8em;">Highlight Box Test</h3>
                    <p style="margin: 0.5em 0 0 0;">This tests gradient backgrounds and white text rendering.</p>
                </div>
            `;

            document.body.appendChild(testDiv);

            try {
                await document.fonts.ready;
                await new Promise(resolve => setTimeout(resolve, 1000));

                const canvas = await html2canvas(testDiv, {
                    scale: 2,
                    backgroundColor: '#ffffff',
                    width: 1410,
                    height: 1000,
                    letterRendering: true,
                    foreignObjectRendering: true
                });

                console.log('Test canvas created:', canvas.width, 'x', canvas.height);

                // Create a temporary link to download the test image
                const link = document.createElement('a');
                link.download = 'pdf-quality-test.png';
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();

                console.log('Quality test image downloaded - check for text clarity and proper scaling');
            } catch (error) {
                console.error('Test capture failed:', error);
            } finally {
                document.body.removeChild(testDiv);
            }
        }

        // Function to validate PDF quality after generation
        function validatePDFQuality() {
            console.log('=== PDF QUALITY VALIDATION ===');
            console.log('After generating PDF, check for:');
            console.log('✓ Text is crisp and readable (not blurry or pixelated)');
            console.log('✓ Font sizes are proportional and consistent');
            console.log('✓ No text stretching or compression');
            console.log('✓ Proper spacing between elements');
            console.log('✓ Icons render correctly');
            console.log('✓ Gradient backgrounds display properly');
            console.log('✓ All slides maintain consistent formatting');
            console.log('✓ Aspect ratio is correct (no distortion)');
            console.log('=== END VALIDATION CHECKLIST ===');
        }

        // Add validation function to window
        window.validatePDFQuality = validatePDFQuality;
    </script>
</body>
</html>
