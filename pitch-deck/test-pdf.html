<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Generation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #ff6b00;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #e55a00;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>PDF Generation Test Suite</h1>
    <p>This page helps test the PDF generation functionality of the pitch deck.</p>

    <div class="test-section">
        <h2>Pre-flight Checks</h2>
        <button class="test-button" onclick="checkLibraries()">Check Required Libraries</button>
        <button class="test-button" onclick="checkSlides()">Check Slide Structure</button>
        <button class="test-button" onclick="openPitchDeck()">Open Pitch Deck</button>
        <div id="preflight-results"></div>
    </div>

    <div class="test-section">
        <h2>PDF Generation Tests</h2>
        <p><strong>Note:</strong> These tests require the pitch deck to be open in another tab.</p>
        <button class="test-button" onclick="testCanvasPDF()">Test Canvas-based PDF</button>
        <button class="test-button" onclick="testPrintPDF()">Test Print-based PDF</button>
        <button class="test-button" onclick="testDebugFunction()">Test Debug Function</button>
        <button class="test-button" onclick="testQualityCapture()">Test Quality Capture</button>
        <button class="test-button" onclick="showQualityChecklist()">Show Quality Checklist</button>
        <div id="pdf-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Manual Testing Instructions</h2>
        <ol>
            <li>Open the pitch deck in a new tab</li>
            <li>Wait for Reveal.js to fully initialize (check console for "Reveal.js initialized successfully")</li>
            <li>Navigate through all slides to ensure they load properly</li>
            <li>Click the "Save as PDF" button on the last slide</li>
            <li>Verify that:
                <ul>
                    <li><strong>All pages in the PDF contain content</strong> (no blank pages)</li>
                    <li><strong>The "Save as PDF" button is not visible</strong> in the PDF</li>
                    <li><strong>Content is properly formatted</strong> and not distorted</li>
                    <li><strong>All slides are included</strong> in the correct order</li>
                    <li><strong>Slide transitions work</strong> during generation (you should see slides changing)</li>
                </ul>
            </li>
            <li>If canvas-based generation fails, verify the print fallback works</li>
        </ol>

        <h3>Latest Quality Improvements:</h3>
        <ul>
            <li><strong>Fixed Aspect Ratio:</strong> Canvas now uses proper A4 landscape dimensions (1410x1000) to prevent distortion</li>
            <li><strong>Enhanced Text Rendering:</strong> Improved font smoothing, letter rendering, and typography scaling</li>
            <li><strong>Better Styling Preservation:</strong> Maintains original slide formatting with enhanced typography</li>
            <li><strong>Optimized Canvas Settings:</strong> Higher scale (2x) with proper DPI and font loading</li>
            <li><strong>Quality Validation:</strong> Built-in testing and validation functions</li>
        </ul>

        <h3>New Testing Shortcuts:</h3>
        <ul>
            <li><strong>Ctrl+Shift+D:</strong> Debug slide information</li>
            <li><strong>Ctrl+Shift+T:</strong> Test canvas capture quality</li>
            <li><strong>Ctrl+Shift+V:</strong> Show PDF quality validation checklist</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Troubleshooting</h2>
        <ul>
            <li><strong>Blank pages:</strong> Check browser console for errors, try the debug function (Ctrl+Shift+D)</li>
            <li><strong>Button visible in PDF:</strong> Verify print CSS is properly applied</li>
            <li><strong>Content distortion:</strong> Check slide dimensions and canvas scaling</li>
            <li><strong>Generation fails:</strong> Check if required libraries (jsPDF, html2canvas) are loaded</li>
        </ul>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function checkLibraries() {
            clearResults('preflight-results');

            // Check if we can access the pitch deck window
            const pitchDeckWindow = window.open('', 'pitchDeck');
            if (!pitchDeckWindow || pitchDeckWindow.closed) {
                addResult('preflight-results', 'Pitch deck window not found. Please open the pitch deck first.', 'error');
                return;
            }

            try {
                const hasJsPDF = typeof pitchDeckWindow.jspdf !== 'undefined';
                const hasHtml2Canvas = typeof pitchDeckWindow.html2canvas !== 'undefined';
                const hasReveal = typeof pitchDeckWindow.Reveal !== 'undefined';
                const revealReady = pitchDeckWindow.revealReady || false;

                addResult('preflight-results', `jsPDF: ${hasJsPDF ? 'Loaded' : 'Missing'}`, hasJsPDF ? 'success' : 'error');
                addResult('preflight-results', `html2canvas: ${hasHtml2Canvas ? 'Loaded' : 'Missing'}`, hasHtml2Canvas ? 'success' : 'error');
                addResult('preflight-results', `Reveal.js: ${hasReveal ? 'Loaded' : 'Missing'}`, hasReveal ? 'success' : 'error');
                addResult('preflight-results', `Reveal.js Ready: ${revealReady ? 'Yes' : 'No'}`, revealReady ? 'success' : 'error');

                if (hasReveal && revealReady) {
                    const currentSlide = pitchDeckWindow.Reveal.getIndices();
                    addResult('preflight-results', `Current slide: ${currentSlide.h + 1}`, 'info');
                }
            } catch (error) {
                addResult('preflight-results', `Error checking libraries: ${error.message}`, 'error');
            }
        }

        function checkSlides() {
            clearResults('preflight-results');
            
            const pitchDeckWindow = window.open('', 'pitchDeck');
            if (!pitchDeckWindow || pitchDeckWindow.closed) {
                addResult('preflight-results', 'Pitch deck window not found. Please open the pitch deck first.', 'error');
                return;
            }

            try {
                const slides = pitchDeckWindow.document.querySelectorAll('.reveal .slides section');
                addResult('preflight-results', `Found ${slides.length} slides`, slides.length > 0 ? 'success' : 'error');

                if (slides.length > 0) {
                    let visibleSlides = 0;
                    slides.forEach((slide, index) => {
                        const style = pitchDeckWindow.getComputedStyle(slide);
                        if (style.display !== 'none') visibleSlides++;
                    });
                    addResult('preflight-results', `${visibleSlides} slides currently visible`, 'info');
                }
            } catch (error) {
                addResult('preflight-results', `Error checking slides: ${error.message}`, 'error');
            }
        }

        function openPitchDeck() {
            window.open('index.html', 'pitchDeck');
            addResult('preflight-results', 'Opened pitch deck in new window', 'success');
        }

        function testCanvasPDF() {
            clearResults('pdf-test-results');
            addResult('pdf-test-results', 'Canvas-based PDF test requires manual verification. Check the generated PDF for blank pages and content quality.', 'info');
        }

        function testPrintPDF() {
            clearResults('pdf-test-results');
            addResult('pdf-test-results', 'Print-based PDF test requires manual verification. Use browser print function and check output.', 'info');
        }

        function testDebugFunction() {
            clearResults('pdf-test-results');

            const pitchDeckWindow = window.open('', 'pitchDeck');
            if (!pitchDeckWindow || pitchDeckWindow.closed) {
                addResult('pdf-test-results', 'Pitch deck window not found. Please open the pitch deck first.', 'error');
                return;
            }

            try {
                if (typeof pitchDeckWindow.debugSlides === 'function') {
                    pitchDeckWindow.debugSlides();
                    addResult('pdf-test-results', 'Debug function executed. Check browser console for slide information.', 'success');
                } else {
                    addResult('pdf-test-results', 'Debug function not found in pitch deck window.', 'error');
                }
            } catch (error) {
                addResult('pdf-test-results', `Error running debug function: ${error.message}`, 'error');
            }
        }

        function testQualityCapture() {
            clearResults('pdf-test-results');

            const pitchDeckWindow = window.open('', 'pitchDeck');
            if (!pitchDeckWindow || pitchDeckWindow.closed) {
                addResult('pdf-test-results', 'Pitch deck window not found. Please open the pitch deck first.', 'error');
                return;
            }

            try {
                if (typeof pitchDeckWindow.testCanvasCapture === 'function') {
                    pitchDeckWindow.testCanvasCapture();
                    addResult('pdf-test-results', 'Quality test executed. Check downloads for test image to verify text quality.', 'success');
                } else {
                    addResult('pdf-test-results', 'Quality test function not found in pitch deck window.', 'error');
                }
            } catch (error) {
                addResult('pdf-test-results', `Error running quality test: ${error.message}`, 'error');
            }
        }

        function showQualityChecklist() {
            clearResults('pdf-test-results');

            const pitchDeckWindow = window.open('', 'pitchDeck');
            if (!pitchDeckWindow || pitchDeckWindow.closed) {
                addResult('pdf-test-results', 'Pitch deck window not found. Please open the pitch deck first.', 'error');
                return;
            }

            try {
                if (typeof pitchDeckWindow.validatePDFQuality === 'function') {
                    pitchDeckWindow.validatePDFQuality();
                    addResult('pdf-test-results', 'Quality checklist displayed in console. Use this to validate your PDF output.', 'success');
                } else {
                    addResult('pdf-test-results', 'Quality validation function not found in pitch deck window.', 'error');
                }
            } catch (error) {
                addResult('pdf-test-results', `Error showing quality checklist: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
