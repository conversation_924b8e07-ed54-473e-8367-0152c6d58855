# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files - NEVER commit these
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# Security sensitive files
firebase-service-account.json
*.key
*.pem
*.p12
*.pfx
secrets/
config/secrets/

# Database files
*.db
*.sqlite
*.sqlite3

# Logs and temporary files
logs/
*.log
tmp/
temp/

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.qodo
