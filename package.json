{"name": "vibtrix-social-media-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "dev:quiet": "node scripts/start-quiet.js", "dev:silent": "./scripts/run-development-quiet.sh", "dev:proxy": "node proxy-server.js", "dev:with-proxy": "concurrently \"npm run dev\" \"npm run dev:proxy\"", "build": "NODE_ENV=production next build", "build:analyze": "node scripts/analyze-bundle.js", "build:safe": "./scripts/build-fixed.sh", "build:legacy": "./scripts/build-without-optimization.sh", "start": "next start -p 3000", "start:quiet": "NODE_ENV=production NEXT_DISABLE_SERVER_LOGS=1 NODE_OPTIONS='--no-warnings' next start -p 3000", "start:proxy": "node proxy-server.js", "start:with-proxy": "concurrently \"npm run start\" \"npm run start:proxy\"", "start:production": "./scripts/run-production-mode.sh", "lint": "next lint", "postinstall": "prisma generate && node scripts/suppress-prisma-logs.js", "create-admin": "tsx scripts/create-admin.ts", "update-admin": "tsx scripts/update-admin-credentials.ts", "db:seed": "tsx prisma/seed.ts", "db:clean": "tsx scripts/clean-db.ts", "init-razorpay": "node scripts/init-razorpay.js", "storage:migrate": "tsx scripts/migrate-files.ts", "optimize": "node scripts/optimize-js.js", "optimize:media": "node scripts/optimize-media.js", "optimize:images": "node scripts/optimize-images.js", "build:optimized": "./scripts/build-optimized.sh", "restore-backup": "node scripts/restore-from-backup.js", "generate-competition-slugs": "tsx scripts/generate-competition-slugs.ts", "check-status": "node scripts/check-app-status.js", "check-db": "node scripts/check-database.js", "security-test": "node scripts/security-test.js", "security-audit": "node scripts/security-audit.js", "security-scan": "node scripts/security-scanner.js", "security-full": "npm run security-audit && npm run security-scan && npm audit", "clear-rate-limits": "node scripts/clear-rate-limits.js"}, "dependencies": {"@hookform/resolvers": "^3.3.1", "@lucia-auth/adapter-prisma": "^4.0.1", "@node-rs/argon2": "^1.8.3", "@prisma/client": "^5.16.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.50.1", "@tanstack/react-query-devtools": "^5.50.1", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@uploadthing/react": "^7.3.1", "arctic": "^1.9.1", "canvas": "^3.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "firebase": "^11.7.1", "firebase-admin": "^13.3.0", "isomorphic-dompurify": "^2.25.0", "jose": "^6.0.11", "ky": "^1.4.0", "lucia": "^3.2.0", "lucide-react": "^0.402.0", "next": "15.0.0-rc.0", "next-themes": "^0.3.0", "node-fetch": "^2.7.0", "prisma": "^5.16.1", "react": "19.0.0-rc-f994737d14-20240522", "react-cropper": "^2.3.3", "react-day-picker": "^9.6.7", "react-dom": "19.0.0-rc-f994737d14-20240522", "react-hook-form": "^7.52.1", "react-image-file-resizer": "^0.4.8", "react-intersection-observer": "^9.10.3", "react-linkify-it": "^1.0.8", "sharp": "^0.34.1", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.2", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.26.10", "@fullhuman/postcss-purgecss": "^7.0.2", "@next/bundle-analyzer": "^15.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "concurrently": "^8.2.2", "cssnano": "^7.0.6", "eslint": "^8", "eslint-config-next": "15.0.0-rc.0", "eslint-config-prettier": "^9.1.0", "http-proxy": "^1.18.1", "postcss": "^8", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.1", "terser": "^5.39.0", "tsx": "^4.7.1", "typescript": "^5"}}