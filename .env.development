# Development environment variables
# These settings help suppress logs in development mode

# Disable Prisma query logging
PRISMA_LOG_QUERIES=false

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1

# Disable React DevTools
REACT_DEVTOOLS_GLOBAL_HOOK=false

# Disable React Query DevTools
REACT_QUERY_DEVTOOLS=false

# Set log level to minimal
LOG_LEVEL=error

# Disable debug output
DEBUG=false

# Disable webpack performance hints
NEXT_WEBPACK_DISABLE_PERFORMANCE_HINTS=1

# Disable Next.js build indicator
NEXT_DISABLE_BUILD_INDICATOR=1

# Disable Next.js server logs
NEXT_DISABLE_SERVER_LOGS=1

# Disable Prisma logs
PRISMA_CLIENT_NO_LOGS=1

# Suppress Node.js warnings
NODE_OPTIONS=--no-warnings

# Disable console output in production builds
DISABLE_CONSOLE_LOGS=true
