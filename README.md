# Vibtrix - Secure Social Media Competition Platform

A full-stack social media app with infinite loading, optimistic updates, authentication, DMs, notifications, file uploads, and much more. Enhanced with enterprise-grade security features.

Watch the free tutorial on YouTube: https://www.youtube.com/watch?v=TyV12oBDsYI

![thumbnail 7](https://github.com/user-attachments/assets/686b37e4-3d16-4bc4-a7f2-9d152c3addf5)

## 🔒 Security Features

Vibtrix implements comprehensive security measures to ensure a safe and secure platform:

### 🛡️ Authentication & Authorization
- **Enhanced JWT Security**: Strong secrets, token rotation, and secure claims
- **Role-Based Access Control (RBAC)**: Hierarchical permissions system
- **Session Security**: Advanced session management with hijacking detection
- **Multi-Factor Authentication**: Support for secure authentication flows

### 🔐 Data Protection
- **Input Validation**: Comprehensive validation using Zod schemas
- **SQL Injection Prevention**: Multiple layers of protection
- **XSS Protection**: HTML sanitization and Content Security Policy
- **CSRF Protection**: Token-based and double-submit cookie protection
- **File Upload Security**: MIME type validation, file signature checking

### 🚦 Rate Limiting & Monitoring
- **Multi-Tier Rate Limiting**: Different limits for various endpoint types
- **Security Monitoring**: Login attempt tracking and suspicious activity detection
- **Real-time Alerts**: Automated security event notifications

### 🔧 Infrastructure Security
- **Security Headers**: Comprehensive HTTP security headers
- **CORS Configuration**: Secure cross-origin resource sharing
- **Environment Security**: Proper secrets management
- **Database Security**: Parameterized queries and secure connections

### 📊 Security Score: 100%

All OWASP Top 10 vulnerabilities have been addressed with enterprise-grade solutions.

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/kevinmanvar27/vibtrix.git
   cd vibtrix
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

4. **Run security tests**
   ```bash
   node scripts/security-test.js
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 📋 Security Checklist

Before deploying to production:

- [ ] All environment variables are properly set
- [ ] Security tests pass (100% score)
- [ ] HTTPS is enabled with valid SSL certificates
- [ ] Database connections are secure
- [ ] Security monitoring is configured
- [ ] Regular security audits are scheduled

## 📚 Documentation

- [Security Documentation](./SECURITY.md) - Comprehensive security guide
- [API Documentation](./docs/api.md) - API endpoints and security
- [Deployment Guide](./docs/deployment.md) - Secure deployment practices

## 🔍 Security Testing

Run the security test suite to verify all security measures:

```bash
node scripts/security-test.js
```

This will check:
- Environment variable security
- File security and gitignore configuration
- Security library implementations
- Middleware security features
- API endpoint protection
- Database security measures

## 🚨 Security Reporting

If you discover a security vulnerability, please:

1. **Do not** create a public issue
2. Email security concerns to the maintainers
3. Include detailed reproduction steps
4. Allow time for the issue to be addressed

## 🤝 Contributing

Please read our security guidelines before contributing:

1. All code must pass security tests
2. Follow secure coding practices
3. Update security documentation as needed
4. Test security features thoroughly

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**⚠️ Security Notice**: This application implements enterprise-grade security measures. Regular security audits and updates are recommended to maintain the highest security standards.
